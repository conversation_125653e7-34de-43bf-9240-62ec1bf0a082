<script setup lang="ts">
import type { BackgroundConfig } from '../../types/interfaces'
import { computed } from 'vue'

const props = defineProps<{ config: BackgroundConfig }>()

// 动态计算样式
const style = computed(() => ({
  '--opacity': props.config.opacity || 0.8,
  '--speed': 8 / (props.config.speed || 1),
  '--color1': props.config.colors?.[0] || '#7877c6',
  '--color2': props.config.colors?.[1] || '#4f46e5',
  '--color3': props.config.colors?.[2] || '#06b6d4',
}))
</script>

<template>
  <div class="aurora-effect" :style="style">
    <div class="aurora-layer" />
    <div class="aurora-layer" />
    <div class="aurora-layer" />
  </div>
</template>

<style scoped>
.aurora-effect {
  position: absolute;
  inset: 0;
}

.aurora-layer {
  position: absolute;
  inset: 0;
  opacity: var(--opacity);
  mix-blend-mode: screen; /* 混合模式让光效更柔和 */
}

/* 通过伪元素和不同的动画延迟创造层次感 */
.aurora-layer:nth-child(1) {
  background: radial-gradient(circle at 10% 20%, var(--color1), transparent 50%);
  animation: move var(--speed)s infinite linear;
  animation-delay: -2s;
}

.aurora-layer:nth-child(2) {
  background: radial-gradient(circle at 80% 30%, var(--color2), transparent 60%);
  animation: move var(--speed)s infinite linear;
  animation-delay: -4s;
}

.aurora-layer:nth-child(3) {
  background: radial-gradient(circle at 50% 90%, var(--color3), transparent 50%);
  animation: move var(--speed)s infinite linear;
}

@keyframes move {
  0% { transform: translate(-10%, -10%) scale(1); }
  25% { transform: translate(10%, 20%) scale(1.2); }
  50% { transform: translate(20%, -15%) scale(1); }
  75% { transform: translate(-15%, 10%) scale(1.3); }
  100% { transform: translate(-10%, -10%) scale(1); }
}
</style>
