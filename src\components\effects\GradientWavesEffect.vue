<script setup lang="ts">
import type { BackgroundConfig } from '../../types/interfaces'
import { computed } from 'vue'

const props = defineProps<{ config: BackgroundConfig }>()

// 将颜色和透明度结合
function getRgba(hex: string, opacity: number) {
  const r = Number.parseInt(hex.slice(1, 3), 16)
  const g = Number.parseInt(hex.slice(3, 5), 16)
  const b = Number.parseInt(hex.slice(5, 7), 16)
  return `rgba(${r}, ${g}, ${b}, ${opacity})`
}

const style = computed(() => ({
  '--opacity': props.config.opacity || 0.8,
  '--speed': 15 / (props.config.speed || 1),
  '--color1': props.config.colors?.[0] || '#4f46e5',
  '--color2': props.config.colors?.[1] || '#06b6d4',
  '--color3': props.config.colors?.[2] || '#7877c6',
  '--rgba1': getRgba(props.config.colors?.[0] || '#4f46e5', (props.config.opacity || 0.8) * 0.5),
  '--rgba2': getRgba(props.config.colors?.[1] || '#06b6d4', (props.config.opacity || 0.8) * 0.5),
}))
</script>

<template>
  <div class="waves-effect" :style="style">
    <div class="wave" />
    <div class="wave" />
    <div class="wave" />
  </div>
</template>

<style scoped>
.waves-effect {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, var(--color1), var(--color2), var(--color3));
  background-size: 400% 400%;
  animation: gradient-bg var(--speed)s ease infinite;
}

@keyframes gradient-bg {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.wave {
  background: var(--rgba1);
  border-radius: 50%;
  position: absolute;
  width: 200vw;
  height: 200vw;
  bottom: -185vw;
  left: -50vw;
  opacity: 0.5;
  animation: wave-move calc(var(--speed) * 1.2)s infinite linear;
}

.wave:nth-child(2) {
  background: var(--rgba2);
  bottom: -180vw;
  animation-delay: -2s;
  animation-duration: calc(var(--speed) * 1.5)s;
}

.wave:nth-child(3) {
  bottom: -182vw;
  animation-delay: -4s;
}

@keyframes wave-move {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
